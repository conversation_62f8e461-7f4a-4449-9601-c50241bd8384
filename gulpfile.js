/*
 * Gulpfile.js
 * Author: <PERSON> (<EMAIL>)
 */

require('dotenv').config();

// Debug env vars
// console.log('BROWSERSYNC_PROXY:', process.env.BROWSERSYNC_PROXY);
// console.log('BROWSERSYNC_HOST:', process.env.BROWSERSYNC_HOST);
// console.log('BROWSERSYNC_NOTIFY:', process.env.BROWSERSYNC_NOTIFY);
// console.log('BROWSERSYNC_OPEN:', process.env.BROWSERSYNC_OPEN);
// console.log('BROWSERSYNC_PORT:', process.env.BROWSERSYNC_PORT);
// console.log('BROWSERSYNC_HTTPS:', process.env.BROWSERSYNC_HTTPS);
// console.log('BROWSERSYNC_KEY:', process.env.BROWSERSYNC_KEY);
// console.log('BROWSERSYNC_CERT:', process.env.BROWSERSYNC_CERT);

// Dev modules
var gulp            = require("gulp"),
    sass            = require('gulp-sass')(require('sass')),
    del             = require('del'),
    postcss         = require("gulp-postcss"),
    autoprefixer    = require("autoprefixer"),
    cssnano         = require("cssnano"),
    uglify          = require("gulp-uglify");

// BrowserSync
var browserSync     = require("browser-sync").create();

// Clean
gulp.task("clean", function() {
    return del(["./assets"]);
})

// CSS
gulp.task("css", function() {
    return (
        gulp
            .src("./src/scss/main.scss")
            .pipe(sass())
            .on("error", sass.logError)
            .pipe(postcss([autoprefixer(), cssnano()]))
            .pipe(gulp.dest("./assets/css"))
            .pipe(browserSync.stream())
    );
})

// JS
gulp.task("js", function() {
    return (
        gulp
            .src(["./src/js/**/*"])
            .pipe(uglify())
            .pipe(gulp.dest("./assets/js"))
            .pipe(browserSync.stream())
    );
})

// Images
gulp.task("img", function() {
    return (
        gulp
            .src("./src/img/**/*")
            .pipe(gulp.dest("./assets/img"))
    );
})

// SVG
gulp.task("svg", function() {
    return (
        gulp
            .src("./src/svg/**/*")
            .pipe(gulp.dest("./assets/svg"))
    );
})

// Swiper
gulp.task("swiper", function() {
    return gulp
        .src("./node_modules/swiper/swiper-bundle.min.{js,css}")
        .pipe(gulp.dest("./assets/swiper"));
});

// Watch files
gulp.task("default", function watchFiles(done) {
    browserSync.init({
        proxy: process.env.BROWSERSYNC_PROXY,
        host: process.env.BROWSERSYNC_HOST,
        notify: process.env.BROWSERSYNC_NOTIFY === 'true',
        open: process.env.BROWSERSYNC_OPEN,
        port: process.env.BROWSERSYNC_PORT,
        https: process.env.BROWSERSYNC_HTTPS === 'true' ? {
            key: process.env.BROWSERSYNC_KEY,
            cert: process.env.BROWSERSYNC_CERT
        } : false
    });

    gulp.watch(["./src/scss/**/*.scss"], gulp.series("css", function cssBrowserReload(done) {
        browserSync.reload();
        done();
    }));
    gulp.watch("./src/js/**/*.js", gulp.series("js", function jsBrowserReload(done) {
        browserSync.reload();
        done();
    }));
    gulp.watch([
        "**/*.php",
        "**/*.html"
    ]).on('change', browserSync.reload);
    done();
})

// Build assets
gulp.task( "build", gulp.series( "clean", gulp.parallel(
    "css",
    "js",
    "img",
    "svg",
    "swiper"
), "default" ) )
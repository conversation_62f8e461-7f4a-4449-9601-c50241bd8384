<?php 
$logo 			  = get_field('logo', 'option');
$footer_1_heading = get_field('footer_1_heading', 'option');
$footer_2_heading = get_field('footer_2_heading', 'option');
$footer_3_heading = get_field('footer_3_heading', 'option');
$footer_2_content = get_field('footer_2_content', 'option');
$footer_3_content = get_field('footer_3_content', 'option');
$footer_3_img 	  = get_field('footer_3_img', 'option');
$copyright 		  = get_field('copyright', 'option');
$copyright_links  = get_field('copyright_links', 'option');
?>
<footer class="footer">
	<div class="container">
		<div class="row flex">
			<div class="col">
				<h5><?php echo esc_html($footer_1_heading); ?></h5>
				<?php
				wp_nav_menu(
					array(
						'theme_location' => 'menu-2',
						'menu_class'     => 'footer-menu',
						'depth'          => 1,
						'container'      => false,
						'fallback_cb'    => false,
						'items_wrap'     => '<ul class="%2$s">%3$s</ul>',
					)
				);
				?>
			</div>
			<div class="col">
				<h5><?php echo esc_html($footer_2_heading); ?></h5>
				<?php echo wp_kses_post($footer_2_content); ?>
			</div>
			<div class="col">
				<h5><?php echo esc_html($footer_3_heading); ?></h5>
				<div class="row flex">
					<div class="col">
						<?php echo wp_kses_post($footer_3_img); ?>
					</div>
					<div class="col">
						<?php echo wp_kses_post($footer_3_content); ?>
					</div>
				</div>
			</div>
		</div>
		<div class="row flex">
			<div class="col">
				<a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
					<img src="<?php echo esc_url($logo['url']); ?>" alt="<?php echo esc_attr($logo['alt']); ?>" />
				</a>
			</div>
			<div class="col">
				<?php echo wp_kses_post($copyright); ?>
			</div>
			<div class="col">
				<?php
				wp_nav_menu(
					array(
						'theme_location' => 'menu-3',
						'menu_class'     => 'footer-menu',
						'depth'          => 1,
						'container'      => false,
						'fallback_cb'    => false,
						'items_wrap'     => '<ul class="%2$s">%3$s</ul>',
					)
				);
				?>
			</div>
			<?php if ($copyright_links) : ?>
				<div class="col">
					<div class="row flex">
						<?php foreach ($copyright_links as $copyright_link) : ?>
							<a href="<?php echo esc_url($copyright_link['link']); ?>">
								<img src="<?php echo esc_url($copyright_link['img']['url']); ?>" alt="<?php echo esc_attr($copyright_link['img']['alt']); ?>" />
							</a>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>
		</div>
	</div>
</footer>

<?php wp_footer(); ?>

</body>
</html>

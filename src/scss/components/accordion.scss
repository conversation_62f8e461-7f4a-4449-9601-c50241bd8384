@use '../abstracts/variables' as variables;

.accordion {
    width: 1040px;
    max-width: 100%;
    margin: 0 auto;

    h3, p {
        text-align: left;
    }

    h3 {
        margin-bottom: 0;
        color: variables.$light-blue;
        display: flex;
        align-items: center;
        user-select: none;
        transition: color 0.3s ease-in-out;

        &:before {
            font-family: "Font Awesome 6 Pro";
            content: "\f138";
            font-weight: 900;
            margin-right: 25px;
            font-size: 28px;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            line-height: 1;
            text-rendering: auto;
        }

        span {
            position: relative;
            display: inline;
            background-image: linear-gradient(to right, variables.$dark-blue, variables.$dark-blue);
            background-size: 0% 1px;
            background-repeat: no-repeat;
            background-position: left bottom;
            transition: background-size 0.3s ease-in-out;

            // Support for multi-line text by using box-decoration-break
            -webkit-box-decoration-break: clone;
            box-decoration-break: clone;

            // Fallback for browsers that don't support background-image underlines
            @supports not (background-image: linear-gradient(to right, variables.$dark-blue, variables.$dark-blue)) {
                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 0;
                    height: 1px;
                    background-color: variables.$dark-blue;
                    transition: width 0.3s ease-in-out;
                    z-index: 1;
                }
            }
        }
    }

    .accordion-item {
        margin-bottom: 20px;
        overflow: hidden;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .accordion-header {
        padding: 10.5px 0;
        cursor: pointer;

        &:hover {
            h3 {
                color: variables.$dark-blue;

                span {
                    background-size: 100% 1px;

                    // Fallback animation for browsers that don't support background-image underlines
                    @supports not (background-image: linear-gradient(to right, variables.$dark-blue, variables.$dark-blue)) {
                        &::after {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    .accordion-content {
        padding-left: 53px;
        height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;

        > :last-child {
            margin-bottom: 0;
        }
    }

    .accordion-item.active {
        .accordion-header h3:before {
            content: "\f13a";
        }

        .accordion-content {
            height: auto;
        }
    }
}
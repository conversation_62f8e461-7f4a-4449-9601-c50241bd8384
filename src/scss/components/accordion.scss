@use '../abstracts/variables' as variables;

.accordion {
    width: 1040px;
    max-width: 100%;
    margin: 0 auto;

    h3, p {
        text-align: left;
    }

    h3 {
        margin-bottom: 0;
        color: variables.$light-blue;
        display: flex;
        align-items: center;
        user-select: none;
        transition: color 0.3s ease-in-out;
        
        &:before {
            font-family: "Font Awesome 6 Pro";
            content: "\f138";
            font-weight: 900;
            margin-right: 25px;
            font-size: 28px;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            line-height: 1;
            text-rendering: auto;
        }

        span {
            position: relative;
            display: inline-block;
            
            &::before {
                content: '';
                display: block;
                opacity: 0;
                width: 100%;
                height: 1px;
                background-color: variables.$dark-blue;
                position: absolute;
                bottom: 0;
                transition: opacity 0.3s ease-in-out;
            }
        }
    }
    
    .accordion-item {
        margin-bottom: 20px;
        overflow: hidden;

        &:last-child {
            margin-bottom: 0;
        }
    }
    
    .accordion-header {
        padding: 10.5px 0;
        cursor: pointer;

        &:hover {
            h3 {
                color: variables.$dark-blue;

                span::before {
                    opacity: 1;
                }
            }
        }
    }
    
    .accordion-content {
        padding-left: 53px;
        height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;

        > :last-child {
            margin-bottom: 0;
        }
    }
    
    .accordion-item.active {
        .accordion-header h3:before {
            content: "\f13a";
        }

        .accordion-content {
            height: auto;
        }
    }
}
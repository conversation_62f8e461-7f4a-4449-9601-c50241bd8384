@use "../abstracts/variables" as variables;

// Typography styles
h1 {
    font-family: variables.$font-heading;
    font-style: normal;
    font-weight: 400;
    font-size: 36px;
    line-height: 43px;
}

h2 {
    font-family: variables.$font-heading;
    font-style: normal;
    font-weight: 400;
    font-size: 30px;
    line-height: 36px;
}

h2.thin {
    font-family: variables.$font-heading;
    font-style: normal;
    font-weight: 325;
    font-size: 30px;
    line-height: 48px;
}

h3 {
    font-family: variables.$font-heading;
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
}

h3.thin {
    font-family: variables.$font-heading;
    font-style: normal;
    font-weight: 325;
    font-size: 20px;
    line-height: 32px;
}

h4 {
    font-family: variables.$font-heading;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 25px;
}

p, li {
    font-family: variables.$font-body;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
}

.text-small {
    font-family: variables.$font-small;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
}
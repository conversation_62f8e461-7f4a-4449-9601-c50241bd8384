// Mobile menu toggle
const menuToggle = document.querySelector('.menu__toggle');
const body = document.querySelector('body');

menuToggle.addEventListener('click', function () {
  body.classList.toggle('mobile-menu-open');
});

// Fix header position for admin bar
if (body.classList.contains('admin-bar')) {
  window.addEventListener('scroll', function () {
    if (window.scrollY > 46) {
      body.classList.add('scrolled-under-admin-bar');
    } else {
      body.classList.remove('scrolled-under-admin-bar');
    }
  });
}

// Accordion functionality
document.addEventListener('DOMContentLoaded', function () {
  const accordionItems = document.querySelectorAll('.accordion-item');

  // Add click event listeners to all accordion headers
  accordionItems.forEach(item => {
    const header = item.querySelector('.accordion-header');

    header.addEventListener('click', function () {
      // Toggle active class on the clicked item
      item.classList.toggle('active');
    });
  });
});

// Use Swiper without ES module imports
const swiper = new Swiper('.swiper-container', {
  loop: true,
  autoplay: {
    delay: 5000,
    disableOnInteraction: false,
  },
  navigation: false,
  effect: 'fade',
  fadeEffect: {
    crossFade: true,
  }
});